# AugmentCode-Free Standalone Executable

## 📦 Package Information

**File**: `AugmentCode-Free.exe`  
**Size**: ~38.7 MB  
**Platform**: Windows 10/11 (64-bit)  
**Dependencies**: None (all included)

## 🚀 Quick Start

### For End Users
1. **Download**: Get the `AugmentCode-Free.exe` file
2. **Run**: Simply double-click the executable file
3. **No Installation Required**: The program runs immediately without any setup

### System Requirements
- **Operating System**: Windows 10 or Windows 11
- **Architecture**: 64-bit (x64)
- **Memory**: Minimum 100MB RAM
- **Storage**: 50MB free space
- **Permissions**: Standard user permissions (no admin required)

## 🔧 Features Included

The standalone executable includes all original functionality:

### Core Features
- ✅ **Enhanced Database Cleaning**: Clean VS Code, Cursor, Windsurf databases
- ✅ **Process Management**: Intelligent IDE process detection and termination
- ✅ **File Cleanup**: Physical file deletion with force delete capabilities
- ✅ **Multiple Cleanup Modes**: database_only, file_only, hybrid, aggressive
- ✅ **Multi-IDE Support**: VS Code, Cursor, Windsurf, JetBrains
- ✅ **Telemetry ID Modification**: Reset or change IDE telemetry identifiers
- ✅ **JetBrains SessionID Management**: Automatic SessionID modification

### GUI Features
- ✅ **Intuitive Interface**: User-friendly graphical interface
- ✅ **IDE Selection**: Dropdown menu for IDE selection
- ✅ **One-Click Operations**: Easy task execution
- ✅ **Multi-language Support**: English and Chinese interfaces
- ✅ **Process Detection**: Automatic IDE process management
- ✅ **User Feedback**: Clear status messages and confirmations

## 📋 Usage Instructions

### Important Pre-Usage Steps
⚠️ **Before using the tool:**
1. **Log out** of your AugmentCode account
2. **Close all IDE instances** (VS Code, Cursor, Windsurf, JetBrains)
3. **Backup important data** if needed
4. The tool will automatically detect and help close running IDEs

### Running the Application
1. **Launch**: Double-click `AugmentCode-Free.exe`
2. **Select IDE**: Choose your target IDE from the dropdown menu
3. **Choose Operation**: Click the desired operation button
4. **Follow Instructions**: The application will guide you through the process

### Available Operations
- **Clean Database**: Remove specific entries from IDE databases
- **Modify Telemetry IDs**: Change IDE telemetry identifiers
- **Modify SessionID**: Update JetBrains SessionID (for JetBrains IDEs)
- **Run All Tools**: Execute all available maintenance operations

## 🛠️ Technical Details

### Build Information
- **PyInstaller Version**: 6.15.0
- **Python Version**: 3.12.6
- **Build Platform**: Windows 11
- **Packaging Mode**: Single file executable (--onefile)
- **GUI Mode**: Windowed application (--windowed)

### Included Dependencies
- **PyQt6**: GUI framework
- **click**: Command-line interface
- **colorama**: Colored terminal output
- **psutil**: Process and system utilities
- **All project modules**: Complete functionality preserved

### Included Resources
- **Configuration files**: `config/settings.json`
- **Language files**: `languages/en_US.json`, `languages/zh_CN.json`
- **GUI components**: Complete `gui_qt6/` module
- **Core tools**: Complete `augment_tools_core/` module

## 🔒 Security & Safety

### File Safety
- **Automatic Backups**: Database backups created before modifications
- **Safe Operations**: All operations are reversible
- **Process Detection**: Prevents conflicts with running IDEs

### Antivirus Notes
- Some antivirus software may flag PyInstaller executables as suspicious
- This is a false positive due to the executable packing method
- The source code is completely open source and available for inspection

## 📁 Distribution

### For Distribution
1. **Single File**: Only `AugmentCode-Free.exe` needs to be distributed
2. **No Dependencies**: Users don't need Python or any libraries installed
3. **Portable**: Can be run from any location (USB drive, desktop, etc.)
4. **No Registry**: Doesn't modify Windows registry

### Recommended Distribution Package
```
AugmentCode-Free-Portable/
├── AugmentCode-Free.exe          # Main executable
├── README.txt                    # Basic usage instructions
└── EXECUTABLE_INSTRUCTIONS.md    # Detailed documentation
```

## 🐛 Troubleshooting

### Common Issues
1. **Antivirus Blocking**: Add exception for the executable
2. **Slow Startup**: First run may be slower due to extraction
3. **Missing GUI**: Ensure Windows display scaling is compatible

### Support
- **Source Code**: https://github.com/BasicProtein/AugmentCode-Free
- **Issues**: Report problems on the GitHub repository
- **Documentation**: Refer to the original project README

## 📄 License

This executable maintains the same license as the original project (MIT License).
The standalone executable is provided as-is for convenience and portability.
