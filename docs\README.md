# AugmentCode-Free 文档中心 / Documentation Center

欢迎来到 AugmentCode-Free 的文档中心！这里包含了所有相关的技术文档、构建指南和故障排除资源。

Welcome to the AugmentCode-Free documentation center! Here you'll find all relevant technical documentation, build guides, and troubleshooting resources.

## 📚 文档列表 / Document List

### 🔨 构建与安装 / Build & Installation

- **[BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md)** - 完整的构建说明 / Complete build instructions
  - Windows 平台构建 / Windows platform build
  - macOS 平台构建 / macOS platform build  
  - Linux 平台构建 / Linux platform build
  - 故障排除指南 / Troubleshooting guide

- **[Installation-Method-EN.md](Installation-Method-EN.md)** - 英文安装方法 / English installation methods

### 🍎 macOS 专用 / macOS Specific

- **[MACOS_TROUBLESHOOTING.md](MACOS_TROUBLESHOOTING.md)** - macOS 故障排除指南 / macOS troubleshooting guide
  - "Failed to create parent directory structure" 错误解决 / Error resolution
  - 应用启动问题 / Application startup issues
  - 安全警告处理 / Security warning handling
  - 权限问题修复 / Permission issue fixes

### 🛠️ 构建脚本 / Build Scripts

- **[build_macos.sh](build_macos.sh)** - macOS 自动构建脚本 / macOS automated build script
  - 解决权限问题 / Solves permission issues
  - 自动化构建流程 / Automated build process
  - 支持双版本构建 / Supports dual version builds

- **[install_linux_deps.sh](install_linux_deps.sh)** - Linux 依赖安装脚本 / Linux dependency installation script

## 🚀 快速开始 / Quick Start

### 1. 构建应用 / Build Application

选择您的平台并按照相应的构建指南操作：
Choose your platform and follow the corresponding build guide:

- **Windows**: 参考 [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md) 中的 Windows 部分
- **macOS**: 使用 [build_macos.sh](build_macos.sh) 或参考 [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md)
- **Linux**: 参考 [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md) 中的 Linux 部分

### 2. 遇到问题？/ Having Issues?

- **macOS 用户 / macOS Users**: 查看 [MACOS_TROUBLESHOOTING.md](MACOS_TROUBLESHOOTING.md)
- **其他问题 / Other Issues**: 查看 [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md) 中的故障排除部分

### 3. 获取帮助 / Get Help

如果文档无法解决您的问题，请：
If the documentation doesn't solve your issue, please:

1. 检查 [GitHub Issues](https://github.com/BasicProtein/AugmentCode-Free/issues) 中是否有类似问题
2. 创建新的 Issue 并提供详细信息
3. 包含您的操作系统、Python 版本和完整错误信息

## 📝 文档贡献 / Documentation Contribution

欢迎贡献文档改进！请：
Contributions to improve documentation are welcome! Please:

1. Fork 项目 / Fork the project
2. 创建文档分支 / Create a documentation branch
3. 提交 Pull Request / Submit a Pull Request

## 📞 联系我们 / Contact Us

- **GitHub**: https://github.com/BasicProtein/AugmentCode-Free
- **Issues**: https://github.com/BasicProtein/AugmentCode-Free/issues

---

*最后更新 / Last Updated: 2025-08-22*
*版本 / Version: v2.0.4*
