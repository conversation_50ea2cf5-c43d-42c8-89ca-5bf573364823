name: Build Multi-Platform Release

on:
  push:
    tags:
      - '*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to build'
        required: true
        default: '2.0.3'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-2022
            platform: windows
            python-version: '3.10'
          - os: macos-13
            platform: macos
            python-version: '3.10'
          - os: ubuntu-22.04
            platform: linux
            python-version: '3.10'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install system dependencies (Linux)
      if: matrix.platform == 'linux'
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libgl1-mesa-glx \
          libegl1-mesa \
          libxrandr2 \
          libxss1 \
          libxcursor1 \
          libxcomposite1 \
          libasound2 \
          libxi6 \
          libxtst6 \
          libglib2.0-0 \
          libgtk-3-0

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller

    - name: Build application (Windows)
      if: matrix.platform == 'windows'
      run: |
        pyinstaller --onefile --windowed --name "AugmentCode-Free-v${{ github.event.inputs.version || github.ref_name }}-windows" --add-data "languages;languages" --add-data "config;config" --hidden-import=PyQt6.QtCore --hidden-import=PyQt6.QtGui --hidden-import=PyQt6.QtWidgets --hidden-import=psutil --collect-all=PyQt6 main.py
      shell: cmd

    - name: Build application (macOS)
      if: matrix.platform == 'macos'
      run: |
        pyinstaller --onefile --windowed \
          --name "AugmentCode-Free-v${{ github.event.inputs.version || github.ref_name }}-macos" \
          --add-data "languages:languages" \
          --add-data "config:config" \
          --hidden-import=PyQt6.QtCore \
          --hidden-import=PyQt6.QtGui \
          --hidden-import=PyQt6.QtWidgets \
          --hidden-import=psutil \
          --collect-all=PyQt6 \
          main.py

    - name: Build application (Linux)
      if: matrix.platform == 'linux'
      run: |
        pyinstaller --onefile \
          --name "AugmentCode-Free-v${{ github.event.inputs.version || github.ref_name }}-linux" \
          --add-data "languages:languages" \
          --add-data "config:config" \
          --hidden-import=PyQt6.QtCore \
          --hidden-import=PyQt6.QtGui \
          --hidden-import=PyQt6.QtWidgets \
          --hidden-import=psutil \
          --collect-all=PyQt6 \
          main.py

    - name: List dist contents (Windows)
      if: matrix.platform == 'windows'
      run: |
        echo "Contents of dist directory:"
        dir dist
      shell: cmd

    - name: List dist contents (Unix)
      if: matrix.platform != 'windows'
      run: |
        echo "Contents of dist directory:"
        ls -la dist/

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.platform }}-build
        path: dist/*
        retention-days: 30

  release:
    needs: build
    runs-on: ubuntu-22.04
    if: startsWith(github.ref, 'refs/tags/') || github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        merge-multiple: true
        path: ./artifacts
    
    - name: Display structure of downloaded files
      run: |
        echo "Downloaded artifacts:"
        find ./artifacts -type f -exec ls -la {} \;
    
    - name: Create Release
      uses: softprops/action-gh-release@v2
      with:
        files: ./artifacts/*
        tag_name: ${{ github.event.inputs.version || github.ref_name }}
        name: AugmentCode-Free v${{ github.event.inputs.version || github.ref_name }}
        body: |
          ## 🎉 AugmentCode-Free v${{ github.event.inputs.version || github.ref_name }} - 多平台发布
          
          ### 📦 下载文件
          - **Windows**: `AugmentCode-Free-v${{ github.event.inputs.version || github.ref_name }}-windows.exe`
          - **macOS**: `AugmentCode-Free-v${{ github.event.inputs.version || github.ref_name }}-macos`
          - **Linux**: `AugmentCode-Free-v${{ github.event.inputs.version || github.ref_name }}-linux`
          
          ### 🚀 安装说明
          
          **Windows:**
          1. 下载 `.exe` 文件
          2. 双击运行（可能需要管理员权限）
          3. 首次运行可能需要允许Windows Defender
          
          **macOS:**
          1. 下载 macOS 版本
          2. 添加执行权限: `chmod +x AugmentCode-Free-*-macos`
          3. 运行: `./AugmentCode-Free-*-macos`
          4. 如遇安全提示，请在系统偏好设置中允许
          
          **Linux:**
          1. 下载 Linux 版本
          2. 添加执行权限: `chmod +x AugmentCode-Free-*-linux`
          3. 运行: `./AugmentCode-Free-*-linux`
          
          ### 🔧 技术改进 v${{ github.event.inputs.version || github.ref_name }}
          - ✅ 修复按钮显示键名问题，现在正确显示中文/英文文本
          - ✅ 修复PyInstaller打包缺失语言文件的问题
          - ✅ 完善所有工作线程和补丁操作的国际化支持
          - ✅ 修复补丁状态"已补丁"等硬编码文本
          - ✅ 支持Windows、macOS、Linux多平台自动构建
          
          ### ⚠️ 重要提示
          - 本项目完全开源免费！
          - 如有人向您收费，请立即举报诈骗行为
          - 项目地址：https://github.com/BasicProtein/AugmentCode-Free
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
