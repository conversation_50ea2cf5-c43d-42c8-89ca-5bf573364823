{"app": {"title": "AugmentCode-Free", "welcome": "欢迎使用", "version": "v2.0.3 - 国际化优化版本", "select_ide": "选择 IDE:", "language": "语言:", "about": "关于", "patch_mode": "补丁模式:", "code_patch": "代码补丁", "operation_log": "操作日志:", "description": "AugmentCode-Free 是一个开源免费的IDE维护工具。", "supported_ides": "支持的IDE:", "ide_list": "• VS Code\n• Cursor\n• Windsurf", "main_features": "主要功能:", "feature_list": "• 清理IDE数据库\n• 修改遥测ID\n• 一键修改所有配置", "opensource_notice": "本项目完全开源免费！", "warning_notice": "⚠️ 重要提示：\n本项目完全开源免费！如果有人向您收费，请立即联系销售方退款并举报诈骗行为。", "project_address": "项目地址：", "dont_show_again": "启动时不再显示此对话框"}, "patch_modes": {"random": "随机假数据", "block": "完全阻止", "empty": "空数据", "stealth": "隐身模式", "debug": "调试模式"}, "buttons": {"run_all": "一键修改所有配置", "close_ide": "关闭选中的IDE", "clean_db": "清理IDE数据库", "modify_ids": "修改IDE遥测ID", "apply_patch": "应用补丁", "restore_files": "恢复文件", "scan_status": "扫描状态", "clear_log": "清空日志", "ok": "确定", "cancel": "取消", "yes": "是", "no": "否"}, "dialogs": {"titles": {"close_confirm": "关闭{ide_name}确认", "run_all_confirm": "一键修改确认", "clean_db_confirm": "清理数据库确认", "modify_ids_confirm": "修改遥测ID确认", "ide_running": "{ide_name}正在运行", "about_title": "关于 AugmentCode-Free", "welcome_title": "欢迎使用 AugmentCode-Free", "language_selection": "选择语言", "jetbrains_notice": "JetBrains 产品提示"}, "messages": {"close_warning": "• 若有未保存的内容请先进行保存\n• {ide_name}中需要备份的聊天记录请先备份\n\n确认无误后才能关闭{ide_name}。\n\n是否继续关闭{ide_name}？", "run_all_warning": "此按钮会关闭{ide_name}并清除Augment聊天数据！\n\n请确保：\n• 文件已保存\n• {ide_name}中的重要聊天记录已备份\n\n是否继续执行一键修改？", "clean_db_warning": "此操作将清理{ide_name}数据库中包含关键字'{keyword}'的条目。\n\n请确保：\n• {ide_name}已关闭\n• 重要数据已备份\n\n是否继续清理数据库？", "modify_ids_warning": "此操作将修改{ide_name}的遥测ID。\n\n请确保：\n• {ide_name}已关闭\n• 重要数据已备份\n\n是否继续修改遥测ID？", "ide_running_warning": "检测到{ide_name}正在运行！\n\n请先关闭{ide_name}再进行操作。\n您可以点击\"关闭选中的IDE\"按钮。", "welcome_message": "欢迎使用 AugmentCode-Free！\n\n这是一个开源免费的IDE维护工具，支持VS Code、Cursor和Windsurf。\n\n请选择您的首选语言：", "first_run_warning": "⚠️ 重要提示：\n\n本项目完全开源免费！\n如果有人向您收费，请立即联系销售方退款并举报诈骗行为。\n\n项目地址：https://github.com/BasicProtein/AugmentCode-Free", "continue_text": "已阅读", "jetbrains_db_notice": "{ide_name} 产品不需要数据库清理。\n\n请使用\"修改IDE遥测ID\"功能来修改 SessionID。"}}, "status": {"success": "✅ 操作完成", "error": "❌ 操作失败", "warning": "⚠️ 注意", "processing": "ℹ️ 处理中...", "ready": "就绪", "running": "正在执行一键修改...", "closing_ide": "正在关闭IDE...", "cleaning_db": "正在清理数据库...", "modifying_ids": "正在修改遥测ID...", "completed": "✅ 所有工具执行已完成", "failed": "❌ 工具执行失败", "not_scanned": "状态: 未扫描"}, "copyright": {"notice": "© 2025 BasicProtein. All rights reserved.", "license": "Licensed under MIT License", "github": "https://github.com/BasicProtein/AugmentCode-Free", "fraud_warning": "⚠️ 本项目完全开源免费！\n如果有人向您收费，请立即联系销售方\n退款并举报诈骗行为。", "open_source": "本项目开源免费", "report_fraud": "如遇付费请举报诈骗"}, "console": {"starting": "🚀 AugmentCode-Free 工具启动中...", "gui_starting": "✅ 正在启动图形界面...", "gui_tip": "💡 提示：如果界面没有出现，请检查是否有防火墙或安全软件阻止", "import_error": "❌ 导入错误", "solutions": "🔧 解决方案：", "install_deps": "1. 确保所有依赖都已安装：pip install -r requirements.txt", "check_python": "2. 确保Python版本为3.7或更高", "check_files": "3. 确保所有项目文件都在同一目录下", "submit_issue": "4. 其他问题请提交issue", "press_enter": "按回车键退出...", "interrupted": "应用程序被用户中断"}, "cli": {"description": "AugmentCode-Free: 多IDE维护工具。提供清理IDE数据库和修改遥测ID的实用程序。支持VS Code、Cursor、Windsurf和JetBrains。", "clean_db_help": "通过删除匹配关键字的条目来清理指定IDE的状态数据库。", "modify_ids_help": "修改指定IDE在storage.json中的遥测ID（machineId、devDeviceId）。", "run_all_help": "为指定IDE运行所有可用工具：clean-db然后modify-ids。", "ide_option_help": "要处理的IDE (vscode, cursor, windsurf, jetbrains)", "keyword_option_help": "要从数据库中搜索和删除的关键字（不区分大小写）。", "keyword_clean_help": "数据库清理的关键字（不区分大小写）。", "executing": "执行中：{operation}", "finished": "进程已完成。", "errors": "进程报告错误。检查之前的消息。", "step": "--- 步骤 {step}: {operation} ---", "error_occurred": "在{step}步骤中发生错误: {error}", "proceeding": "尽管出现错误，仍继续下一步。", "all_finished": "{ide_name}的所有工具已完成执行序列。", "unsupported_ide": "不支持的IDE: {ide}。支持的IDE: vscode, cursor, windsurf, jetbrains"}, "workers": {"close_ide": {"closing": "正在关闭 {ide_name}...", "closed_process": "已关闭 {process_name} (PID: {pid})", "close_failed": "关闭进程失败: {error}", "success": "{ide_name} 已成功关闭", "not_found": "未找到运行中的 {ide_name} 进程", "error": "关闭 {ide_name} 时发生错误: {error}"}, "clean_db": {"starting": "开始清理 {ide_name} 数据库 (关键字: '{keyword}')", "cleaning": "正在清理 {ide_name} 数据库...", "entries_removed": "成功删除 {count} 个包含关键字的条目", "no_entries": "未找到需要清理的条目", "backup_created": "已自动创建数据库备份", "completed": "数据库清理过程完成。", "failed": "数据库清理失败: {error}", "error": "清理数据库时发生错误: {error}"}, "modify_ids": {"starting": "开始修改 {ide_name} 遥测 ID", "modifying": "正在修改 {ide_name} 遥测ID...", "completed": "遥测 ID 修改过程完成。", "failed": "遥测 ID 修改过程报告错误。请检查之前的消息。", "error": "修改遥测 ID 时发生错误: {error}"}, "run_all": {"starting": "开始为 {ide_name} 执行所有工具", "running": "正在执行一键修改...", "step_close": "步骤 1: 关闭 {ide_name}", "step_clean": "步骤 2: 清理 {ide_name} 数据库", "step_modify": "步骤 3: 修改 {ide_name} 遥测ID", "db_completed": "数据库清理完成", "db_failed": "数据库清理失败", "ids_completed": "遥测ID修改完成", "ids_failed": "遥测ID修改失败", "all_completed": "{ide_name}所有工具已完成执行序列。", "partial_failed": "{ide_name}部分工具执行失败。", "error": "运行所有工具时发生错误: {error}"}, "enhanced_cleanup": {"starting": "开始增强清理 {ide_name} (模式: {mode})", "running": "正在执行增强清理...", "results": "清理结果:", "db_cleaned": "  数据库清理: 删除 {count} 个条目", "files_deleted": "  文件删除: {count} 个文件", "global_storage": "    - globalStorage: {count} 个", "workspace_storage": "    - workspaceStorage: {count} 个", "processes_killed": "  进程终止: {count} 个进程", "completed": "增强清理完成。", "failed": "增强清理失败", "error": "增强清理时发生错误: {error}"}, "process_manager": {"checking": "检查 {ide_name} 进程...", "found_processes": "找到 {count} 个 {ide_name} 进程:", "no_processes": "未找到 {ide_name} 进程", "killing": "终止 {ide_name} 进程...", "all_killed": "所有进程已成功终止", "partial_killed": "部分进程可能无法终止", "error": "进程管理时发生错误: {error}"}}, "patch": {"searching": "🔍 正在查找扩展文件...", "not_found": "未找到 {ide_type} 的扩展文件", "found_files": "📁 找到 {count} 个扩展文件", "processing": "🔧 正在处理文件 {current}/{total}: {file}", "already_patched": "⏭️ 文件已补丁，跳过: {file}", "patch_success": "✅ 补丁成功: {file}", "patch_failed": "❌ 补丁失败: {message}", "completed": "补丁操作完成！成功处理 {success}/{total} 个文件", "no_success": "没有文件被成功补丁", "error": "补丁操作失败: {error}", "restoring": "🔄 正在恢复文件 {current}/{total}: {file}", "restore_success": "✅ 恢复成功: {file}", "restore_skipped": "⚠️ 恢复跳过: {message}", "restore_completed": "恢复操作完成！成功恢复 {success}/{total} 个文件", "restore_no_success": "没有文件需要恢复或恢复失败", "restore_error": "恢复操作失败: {error}", "scanning": "🔍 开始扫描所有IDE的扩展文件...", "scanning_ide": "📂 正在扫描 {ide_type}...", "scan_completed": "✅ {ide_type} 扫描完成，找到 {count} 个文件", "scan_finished": "🎉 扫描完成！", "scan_error": "扫描失败: {error}", "batch_starting": "🚀 开始批量补丁操作...", "batch_processing": "🔧 正在处理 {ide_type} ({current}/{total})...", "batch_no_files": "未找到扩展文件", "batch_success": "成功补丁 {success}/{total} 个文件", "batch_completed": "批量补丁完成！成功处理 {count} 个IDE", "batch_partial": "批量补丁部分成功，请查看详细结果", "batch_error": "批量补丁失败: {error}", "status": {"patched": "已补丁", "not_patched": "未补丁", "file_not_found": "文件不存在", "status_unknown": "状态未知"}}}