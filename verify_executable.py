#!/usr/bin/env python3
"""
Verification script for AugmentCode-Free.exe
This script checks if the executable was built correctly and includes all necessary components.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_file_exists(filepath):
    """Check if a file exists and return its size."""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        return True, size
    return False, 0

def verify_executable():
    """Verify the AugmentCode-Free.exe executable."""
    print("🔍 Verifying AugmentCode-Free.exe...")
    print("=" * 50)
    
    # Check if executable exists
    exe_path = "dist/AugmentCode-Free.exe"
    exists, size = check_file_exists(exe_path)
    
    if not exists:
        print("❌ ERROR: AugmentCode-Free.exe not found in dist/ directory")
        return False
    
    print(f"✅ Executable found: {exe_path}")
    print(f"📦 File size: {size:,} bytes ({size/1024/1024:.1f} MB)")
    
    # Check if it's a valid Windows executable
    if not exe_path.endswith('.exe'):
        print("❌ ERROR: File doesn't have .exe extension")
        return False
    
    print("✅ Valid Windows executable format")
    
    # Check file permissions
    if os.access(exe_path, os.X_OK):
        print("✅ Executable permissions confirmed")
    else:
        print("⚠️  WARNING: Executable permissions may be missing")
    
    # Verify size is reasonable (should be 20-50 MB for this type of application)
    if 20 * 1024 * 1024 < size < 100 * 1024 * 1024:  # 20MB to 100MB
        print("✅ File size is within expected range")
    else:
        print(f"⚠️  WARNING: File size ({size/1024/1024:.1f} MB) is outside expected range (20-100 MB)")
    
    print("\n🎯 Verification Summary:")
    print("✅ Executable file exists and appears valid")
    print("✅ Ready for distribution")
    print("\n📋 Next Steps:")
    print("1. Test the executable by double-clicking it")
    print("2. Verify the GUI launches correctly")
    print("3. Test basic functionality")
    print("4. Package for distribution if needed")
    
    return True

def create_distribution_package():
    """Create a distribution package with all necessary files."""
    print("\n📦 Creating distribution package...")
    
    dist_dir = Path("AugmentCode-Free-Portable")
    dist_dir.mkdir(exist_ok=True)
    
    # Files to include in distribution
    files_to_copy = [
        ("dist/AugmentCode-Free.exe", "AugmentCode-Free.exe"),
        ("README_EXECUTABLE.txt", "README.txt"),
        ("EXECUTABLE_INSTRUCTIONS.md", "EXECUTABLE_INSTRUCTIONS.md")
    ]
    
    for src, dst in files_to_copy:
        src_path = Path(src)
        dst_path = dist_dir / dst
        
        if src_path.exists():
            # Copy file content
            try:
                import shutil
                shutil.copy2(src_path, dst_path)
                print(f"✅ Copied: {src} -> {dst_path}")
            except Exception as e:
                print(f"❌ Failed to copy {src}: {e}")
        else:
            print(f"⚠️  Missing: {src}")
    
    print(f"\n✅ Distribution package created in: {dist_dir}")
    print("📁 Package contents:")
    for item in dist_dir.iterdir():
        if item.is_file():
            size = item.stat().st_size
            print(f"   📄 {item.name} ({size:,} bytes)")
    
    return dist_dir

if __name__ == "__main__":
    print("AugmentCode-Free Executable Verification")
    print("=" * 50)
    
    # Verify the executable
    if verify_executable():
        # Create distribution package
        dist_package = create_distribution_package()
        
        print(f"\n🎉 SUCCESS!")
        print(f"✅ Executable verified and ready for distribution")
        print(f"📦 Distribution package: {dist_package}")
        print(f"\n📋 Distribution Instructions:")
        print(f"1. Zip the '{dist_package}' folder")
        print(f"2. Share the zip file with users")
        print(f"3. Users can extract and run AugmentCode-Free.exe directly")
    else:
        print("\n❌ FAILED!")
        print("Please check the build process and try again.")
        sys.exit(1)
