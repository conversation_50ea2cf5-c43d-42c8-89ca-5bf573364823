AugmentCode-Free Standalone Executable
=====================================

QUICK START:
1. Double-click "AugmentCode-Free.exe" to run
2. No installation or Python required!

IMPORTANT - BEFORE USING:
- Log out of your AugmentCode account
- Close all IDE programs (VS Code, Cursor, Windsurf, etc.)
- Backup important data if needed

WHAT THIS TOOL DOES:
- Cleans IDE databases and cache
- Modifies telemetry IDs for fresh starts
- Manages IDE processes automatically
- Supports multiple IDEs: VS Code, Cursor, Windsurf, JetBrains

SYSTEM REQUIREMENTS:
- Windows 10 or 11 (64-bit)
- 100MB RAM minimum
- 50MB free disk space

TROUBLESHOOTING:
- If antivirus blocks it, add an exception
- First run may be slower than subsequent runs
- For detailed help, see EXECUTABLE_INSTRUCTIONS.md

SOURCE CODE:
https://github.com/BasicProtein/AugmentCode-Free

This is free, open-source software. No payment required!
